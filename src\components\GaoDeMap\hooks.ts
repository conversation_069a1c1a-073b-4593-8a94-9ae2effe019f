import AMapLoader from '@amap/amap-jsapi-loader';
import { useCallback, useEffect, useRef, useState } from 'react';

// 深圳市的默认坐标 [经度, 纬度]
export const DEFAULT_LOCATION: [number, number] = [114.085947, 22.547];

/**
 * 加载高德地图API的自定义Hook
 * 只负责加载API，不创建地图实例
 */
export const useMapAPI = (enableCluster: boolean = false) => {
  const [AMap, setAMap] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const isLoaded = useRef<boolean>(false);

  useEffect(() => {
    // 如果已经加载过API，不再重复加载
    if (isLoaded.current && AMap) {
      return;
    }

    // 设置安全密钥
    try {
      (window as any)._AMapSecurityConfig = {
        securityJsCode: AMAP_CONFIG.securityJsCode, // 高德地图安全密钥
      };

      // 准备插件列表
      const plugins = [
        'AMap.ToolBar',
        'AMap.Scale',
        'AMap.Geocoder',
        'AMap.TileLayer',
        'AMap.Buildings',
        'AMap.DistrictSearch',
        'AMap.PlaceSearch',
        'AMap.IndoorMap',
        'AMap.HawkEye', // 鹰眼插件
        'AMap.ControlBar', // 地图控制插件
      ];

      // 按需加载聚合插件
      if (enableCluster) {
        plugins.push('AMap.MarkerClusterer');
      }

      // 加载高德地图JS API
      AMapLoader.load({
        key: AMAP_CONFIG.key, // 高德地图 API Key
        version: '2.0',
        plugins: plugins, // 按需加载插件列表
      })
        .then((AMapInstance) => {
          console.log('高德地图JS API加载成功');
          setAMap(AMapInstance);
          isLoaded.current = true;
          setLoading(false);
        })
        .catch((e) => {
          console.error('地图加载失败：', e);
          setError('地图加载失败，请刷新页面重试');
          setLoading(false);
        });
    } catch (e) {
      console.error('地图初始化错误：', e);
      setError('地图初始化错误，请检查配置');
      setLoading(false);
    }

    // 组件卸载时不需要清理AMap，因为它是全局资源
  }, [enableCluster]);

  return { AMap, loading, error, isLoaded: isLoaded.current };
};

/**
 * 获取城市位置的函数
 * 根据提供的参数获取地图中心点坐标
 */
export const getCityLocation = (
  AMap: any,
  {
    activedMarker,
    center,
    city,
    timeoutMs = 5000,
  }: {
    activedMarker?: Marker;
    center?: [number, number];
    city?: string;
    timeoutMs?: number;
  },
) => {
  return new Promise<[number, number]>((resolve) => {
    // 如果有激活的标记点，使用激活点的位置作为中心
    if (activedMarker) {
      console.log('使用激活标记点的坐标', activedMarker.position);
      resolve(activedMarker.position);
      return;
    }

    // 如果提供了中心点坐标，直接使用
    if (center) {
      console.log('使用提供的中心点坐标', center);
      resolve(center);
      return;
    }

    // 如果没有提供城市名称，使用默认位置
    if (!city) {
      console.log('未提供城市名称，使用默认位置');
      resolve(DEFAULT_LOCATION);
      return;
    }

    console.log('开始通过城市名称获取坐标，城市:', city);
    // 通过城市名称获取坐标
    try {
      const geocoder = new AMap.Geocoder({
        city: '全国', // 城市，默认全国
        radius: 1000, // 范围，默认1000米
        extensions: 'all', // 返回基本+附加地址信息
      });

      // 添加错误处理和超时处理
      const timeoutPromise = new Promise<[number, number]>((_, reject) => {
        setTimeout(() => {
          console.warn('getLocation请求超时');
          reject(new Error('Geocoder请求超时'));
        }, timeoutMs);
      });

      const geocoderPromise = new Promise<[number, number]>((resolveGeo) => {
        geocoder.getLocation(city, (status: string, result: any) => {
          if (
            status === 'complete' &&
            result.geocodes &&
            result.geocodes.length
          ) {
            const lnglat = (result.geocodes as AmapGeocodeResult[]).find(
              (g) => g.level === '市',
            )?.location;
            if (lnglat) {
              console.log('成功获取城市坐标:', [lnglat.lng, lnglat.lat]);
              resolveGeo([lnglat.lng, lnglat.lat]);
            } else {
              console.warn('获取城市坐标失败，结果:', JSON.stringify(result));
              resolveGeo(DEFAULT_LOCATION);
            }
          } else {
            // 获取失败时使用默认坐标
            console.warn(
              '获取城市坐标失败，状态:',
              status,
              '结果:',
              JSON.stringify(result),
            );
            resolveGeo(DEFAULT_LOCATION);
          }
        });
      });

      // 使用Promise.race来处理超时情况
      Promise.race([geocoderPromise, timeoutPromise])
        .then((location) => resolve(location))
        .catch((error) => {
          console.error('Geocoder错误:', error);
          resolve(DEFAULT_LOCATION);
        });
    } catch (error) {
      console.error('Geocoder异常:', error);
      resolve(DEFAULT_LOCATION);
    }
  });
};

/**
 * 创建和管理地图实例的自定义Hook
 */
export const useMapInstance = ({
  AMap,
  mapContainer,
  center,
  city = '西安',
  markers = [],
  zoom = 11,
  activedZoom = 15,
  mapOptions = {},
  enableCluster = false,
  enableInfoWindow = true,
  events = {},
  onMapCreated,
  customControls = [],
  timeoutMs = 5000,
  markerRender,
}: {
  AMap: any;
  mapContainer: React.RefObject<HTMLDivElement>;
  center?: [number, number];
  city?: string;
  markers?: Array<Marker>;
  zoom?: number;
  activedZoom?: number;
  mapOptions?: {
    features?: string[];
    viewMode?: '2D' | '3D';
    pitch?: number;
    buildingAnimation?: boolean;
    showBuildingBlock?: boolean;
    showLabel?: boolean;
    skyColor?: string;
  };
  enableCluster?: boolean;
  enableInfoWindow?: boolean;
  events?: MapEvents;
  onMapCreated?: (mapInstance: any) => void;
  customControls?: Array<{
    position: 'LT' | 'RT' | 'LB' | 'RB';
    content: React.ReactNode;
    offset?: [number, number];
  }>;
  timeoutMs?: number;
  markerRender?: (marker: Marker) => string;
}) => {
  const mapInstance = useRef<any>(null);
  const markerRefs = useRef<{ [key: string]: any }>({});
  const clusterRef = useRef<any>(null);
  const infoWindowRef = useRef<any>(null);
  const [mapReady, setMapReady] = useState<boolean>(false);

  // 查找激活的标记点
  const activedMarker = markers.find((marker) => marker.actived);

  // 记录上一次的标记点数据，用于比较变化
  const prevMarkersRef = useRef<Array<Marker>>([]);

  // 当标记点数据变化时，记录日志
  useEffect(() => {
    if (markers.length > 0) {
      console.log('标记点数据已更新:', markers);
      console.log('激活的标记点:', activedMarker);
      prevMarkersRef.current = markers;
    }
  }, [markers, activedMarker]);

  // 更新地图中心点和缩放级别，但不重新创建地图实例
  useEffect(() => {
    if (!mapReady || !mapInstance.current || !AMap) {
      return;
    }

    // 更新地图中心点和缩放级别
    getCityLocation(AMap, { activedMarker, center, city, timeoutMs }).then(
      (location) => {
        console.log('更新地图中心点和缩放级别:', location);
        // 添加额外检查，确保mapInstance.current仍然存在
        if (mapInstance.current) {
          mapInstance.current.setCenter(location);
          mapInstance.current.setZoom(activedMarker ? activedZoom : zoom);
        } else {
          console.warn('地图实例已被销毁，无法更新中心点和缩放级别');
        }
      },
    );
  }, [
    mapReady,
    AMap,
    center,
    city,
    zoom,
    activedZoom,
    activedMarker,
    timeoutMs,
  ]);

  // 创建和初始化地图
  useEffect(() => {
    // 如果AMap未加载或容器不存在，不执行
    if (!AMap || !mapContainer.current) {
      return;
    }

    // 如果地图实例已存在，不重新创建
    if (mapInstance.current) {
      return;
    }

    // 获取位置并初始化地图
    getCityLocation(AMap, { activedMarker, center, city, timeoutMs }).then(
      (location) => {
        console.log('获取位置成功，准备初始化地图，位置:', location);

        if (mapContainer.current && !mapInstance.current) {
          // 创建地图实例
          const map = new AMap.Map(mapContainer.current, {
            zoom: activedMarker ? activedZoom : zoom,
            center: location,
            viewMode: mapOptions?.viewMode || '2D',
            resizeEnable: true,
            features: mapOptions?.features || [
              'bg',
              'road',
              'building',
              'point',
            ],
            pitch: mapOptions?.pitch || 0,
            buildingAnimation: mapOptions?.buildingAnimation,
            showBuildingBlock: mapOptions?.showBuildingBlock,
            showLabel: true,
            expandZoomRange: true,
            zooms: [3, 20], // 启用缩放范围
            skyColor: mapOptions?.skyColor,
            preloadMode: true, // 开启预加载模式
            preloadRadius: 1200, // 增加预加载半径
            dragEnable: true,
            touchZoom: true,
            jogEnable: true,
            showIndoorMap: true, // 显示室内地图
            defaultLayer: false, // 禁用默认图层，避免重复
          });

          // 设置地图样式
          map.setMapStyle('amap://styles/normal'); // 使用标准样式

          // 添加基础瓦片图层
          const baseLayer = new AMap.TileLayer({
            zooms: [3, 20],
            visible: true,
            zIndex: 1, // 设置基础图层的层级
          });
          map.add(baseLayer);

          // 注意：卫星图层、路网图层和建筑物图层在 onMapCreated 回调中添加
          // 避免重复添加图层

          // 添加工具条和比例尺控件
          map.addControl(new AMap.ToolBar());
          map.addControl(new AMap.Scale());

          // 创建信息窗体
          if (enableInfoWindow) {
            infoWindowRef.current = new AMap.InfoWindow({
              offset: new AMap.Pixel(0, -30),
              closeWhenClickMap: true,
            });
          }

          // 保存地图实例到ref中
          mapInstance.current = map;
          setMapReady(true);

          // 调用地图创建完成回调
          if (onMapCreated) {
            onMapCreated(map);
          }
        }
      },
    );

    // 组件卸载时清理地图实例
    return () => {
      if (mapInstance.current) {
        console.log('销毁地图实例');
        mapInstance.current.destroy();
        mapInstance.current = null;
        setMapReady(false);
      }
    };
  }, [AMap, mapContainer]); // 只依赖AMap和mapContainer，避免其他props变化时重新创建地图实例

  // 处理标记点
  useEffect(() => {
    if (!mapReady || !mapInstance.current || !AMap) {
      return;
    }

    // 获取当前标记点的ID列表
    const currentMarkerIds = markers.map(
      (marker, index) => marker.id || `marker-${index}`,
    );

    // 找出需要移除的标记点（在旧列表中但不在新列表中）
    const markersToRemove = Object.keys(markerRefs.current).filter(
      (id) => !currentMarkerIds.includes(id),
    );

    // 移除不再需要的标记点
    markersToRemove.forEach((id) => {
      if (markerRefs.current[id]) {
        markerRefs.current[id].setMap(null);
        delete markerRefs.current[id];
      }
    });

    // 如果没有标记点，清除聚合管理器并返回
    if (markers.length === 0) {
      if (clusterRef.current) {
        clusterRef.current.setMap(null);
        clusterRef.current = null;
      }
      return;
    }

    console.log('更新标记点，数量:', markers.length);

    // 创建或更新标记点
    const markersList: any[] = [];

    markers.forEach((marker, index) => {
      const markerId = marker.id || `marker-${index}`;
      let aMapMarker = markerRefs.current[markerId];

      // 为标记点设置选项
      const markerOptions: any = {
        position: marker.position,
        title: marker.title,
        extData: { ...marker, index },
      };

      // 处理自定义渲染 - 优先使用markerRender函数
      if (markerRender) {
        // 自定义渲染函数优先级最高
        markerOptions.content = markerRender(marker);
      } else if (marker.icon) {
        // 处理自定义图标
        if (typeof marker.icon === 'string') {
          markerOptions.icon = marker.icon;
        } else {
          markerOptions.icon = new AMap.Icon({
            image: marker.icon.image,
            size: marker.icon.size
              ? new AMap.Size(marker.icon.size[0], marker.icon.size[1])
              : undefined,
            imageOffset: marker.icon.imageOffset
              ? new AMap.Pixel(
                  marker.icon.imageOffset[0],
                  marker.icon.imageOffset[1],
                )
              : undefined,
            imageSize: marker.icon.imageSize
              ? new AMap.Size(
                  marker.icon.imageSize[0],
                  marker.icon.imageSize[1],
                )
              : undefined,
          });

          if (marker.icon.offset) {
            markerOptions.offset = new AMap.Pixel(
              marker.icon.offset[0],
              marker.icon.offset[1],
            );
          }
        }
      }
      if (marker.actived) {
        // 对激活的标记点应用特殊样式
        // 添加动画效果
        // TODO: 这个属性无效？
        markerOptions.animation = 'AMAP_ANIMATION_BOUNCE';
      }

      // 如果标记点已存在，更新它
      if (aMapMarker) {
        // 先从地图上移除
        aMapMarker.setMap(null);

        // 更新标记点属性
        aMapMarker.setPosition(markerOptions.position);
        aMapMarker.setTitle(markerOptions.title);
        aMapMarker.setExtData(markerOptions.extData);

        if (markerOptions.icon) {
          aMapMarker.setIcon(markerOptions.icon);
        }

        if (markerOptions.content) {
          aMapMarker.setContent(markerOptions.content);
        }

        if (markerOptions.offset) {
          aMapMarker.setOffset(markerOptions.offset);
        }

        if (markerOptions.animation) {
          aMapMarker.setAnimation?.(markerOptions.animation);
        }

        if (markerOptions.zIndex) {
          aMapMarker.setzIndex(markerOptions.zIndex);
        }
      } else {
        // 创建新标记点
        aMapMarker = new AMap.Marker(markerOptions);

        // 添加点击事件
        if (events.onMarkerClick || enableInfoWindow) {
          aMapMarker.on('click', (e: any) => {
            // 如果有信息窗体内容，显示信息窗体
            if (enableInfoWindow && marker.content && infoWindowRef.current) {
              infoWindowRef.current.setContent(marker.content);
              infoWindowRef.current.open(
                mapInstance.current,
                aMapMarker.getPosition(),
              );
            }

            // 调用标记点点击回调
            if (events.onMarkerClick) {
              events.onMarkerClick(marker, e);
            }
          });
        }

        // 存储标记点引用
        markerRefs.current[markerId] = aMapMarker;
      }

      // 将标记点添加到地图上
      aMapMarker.setMap(mapInstance.current);
      markersList.push(aMapMarker);
    });

    // 处理聚合 - 只有在启用聚合且有多个标记点时才使用
    if (enableCluster && markersList.length > 1) {
      // 清除现有聚合管理器
      if (clusterRef.current) {
        clusterRef.current.setMap(null);
        clusterRef.current = null;
      }

      // 创建新的聚合管理器
      clusterRef.current = new AMap.MarkerClusterer(
        mapInstance.current,
        markersList,
        {
          gridSize: 80,
          maxZoom: 16,
          minClusterSize: 2,
          styles: [
            {
              content: (count: number) =>
                `<div style="background-color: rgba(0, 161, 214, 0.8); color: white; width: 36px; height: 36px; line-height: 36px; text-align: center; border-radius: 50%">${count}</div>`,
              offset: new AMap.Pixel(-18, -18),
            },
          ],
        },
      );
    }
  }, [
    AMap,
    mapReady,
    markers,
    enableCluster,
    enableInfoWindow,
    events.onMarkerClick,
    markerRender,
  ]);

  // 处理事件监听
  useEffect(() => {
    if (!mapReady || !mapInstance.current) {
      return;
    }

    // 清除现有事件监听
    mapInstance.current.clearEvents('click');
    mapInstance.current.clearEvents('dragend');
    mapInstance.current.clearEvents('zoomchange');

    // 添加新的事件监听
    if (events.onClick) {
      mapInstance.current.on('click', events.onClick);
    }

    if (events.onDragEnd) {
      mapInstance.current.on('dragend', events.onDragEnd);
    }

    if (events.onZoomChange) {
      mapInstance.current.on('zoomchange', () => {
        if (events.onZoomChange) {
          events.onZoomChange(mapInstance.current.getZoom());
        }
      });
    }
  }, [mapReady, events.onClick, events.onDragEnd, events.onZoomChange]);

  // 处理自定义控件
  useEffect(() => {
    // 增加安全检查，确保地图实例存在且已准备好
    if (
      !mapReady ||
      !mapInstance.current ||
      !AMap ||
      customControls.length === 0
    ) {
      return;
    }

    // 再次检查地图实例是否有效，防止在执行过程中被销毁
    try {
      // 获取地图容器
      const container = mapInstance.current.getContainer();
      if (!container) {
        console.warn('地图容器不存在，可能已被销毁');
        return;
      }

      // 移除现有的自定义控件容器
      const existingControls = container.querySelectorAll(
        '[id^="custom-control-"]',
      );
      existingControls.forEach((el: Element) => {
        try {
          container.removeChild(el);
        } catch (e) {
          console.warn('移除控件失败:', e);
        }
      });

      console.log('添加自定义控件，数量:', customControls.length);
      customControls.forEach((control, index) => {
        try {
          // 创建自定义控件容器
          const customControlContainer = document.createElement('div');
          customControlContainer.id = `custom-control-${index}`;
          customControlContainer.className = 'customControl'; // 使用样式类名

          // 设置控件位置
          let position = 'right-top';
          switch (control.position) {
            case 'LT':
              position = 'left-top';
              break;
            case 'RT':
              position = 'right-top';
              break;
            case 'LB':
              position = 'left-bottom';
              break;
            case 'RB':
              position = 'right-bottom';
              break;
          }

          // 再次检查地图实例是否有效
          if (!mapInstance.current) {
            console.warn('地图实例已被销毁，无法添加控件');
            return;
          }

          // 创建自定义控件
          const customControl = new AMap.Control({
            position: position,
            offset: control.offset
              ? new AMap.Pixel(control.offset[0], control.offset[1])
              : new AMap.Pixel(10, 10),
          });

          // 不再尝试直接添加React节点，而是创建一个空的容器
          // React组件将通过ReactDOM.createPortal在index.tsx中渲染
          customControl.setDomsEvents = () => {};

          // 添加控件前再次检查地图实例
          if (mapInstance.current) {
            customControl.addTo(mapInstance.current);
            // 将容器添加到地图中
            container.appendChild(customControlContainer);
          } else {
            console.warn('地图实例已被销毁，无法添加控件');
          }
        } catch (e) {
          console.error('添加自定义控件失败:', e);
        }
      });
    } catch (e) {
      console.error('处理自定义控件时发生错误:', e);
    }

    // 组件卸载时清理控件
    return () => {
      try {
        const container = mapInstance.current?.getContainer();
        if (container) {
          const existingControls = container.querySelectorAll(
            '[id^="custom-control-"]',
          );
          existingControls.forEach((el: Element) => {
            try {
              container.removeChild(el);
            } catch (e) {
              console.warn('清理控件失败:', e);
            }
          });
        }
      } catch (e) {
        console.warn('清理控件时发生错误:', e);
      }
    };
  }, [AMap, mapReady, customControls]);

  return { mapInstance: mapInstance.current, mapReady };
};

/**
 * 地址解析Hook
 * @param longitude 经度
 * @param latitude 纬度
 * @returns 地址信息和加载状态
 */
export const useReverseGeocode = (
  longitude?: number | string,
  latitude?: number | string,
) => {
  const [address, setAddress] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAddress = useCallback(async () => {
    if (!longitude || !latitude) {
      setAddress('');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { reverseGeocode } = await import('./utils');
      const result = await reverseGeocode(longitude, latitude);
      setAddress(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '地址解析失败';
      setError(errorMessage);

      // 如果是API加载失败，不显示"地址解析失败"，而是保持空白
      if (
        errorMessage.includes('高德地图API') ||
        errorMessage.includes('AMapLoader')
      ) {
        setAddress('');
      } else {
        setAddress('地址解析失败');
      }
    } finally {
      setLoading(false);
    }
  }, [longitude, latitude]);

  useEffect(() => {
    fetchAddress();
  }, [fetchAddress]);

  return {
    address,
    loading,
    error,
    refetch: fetchAddress,
  };
};
