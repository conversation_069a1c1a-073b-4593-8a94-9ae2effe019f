import {
  historicalElementData,
  regionDict,
  typeDict,
} from '@/services/mockData';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';

const { Title } = Typography;
const { TextArea } = Input;

const AdminHistoricalElement: React.FC = () => {
  const [data, setData] = useState(historicalElementData);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [form] = Form.useForm();

  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingItem(record);
    const formData = {
      ...record,
      construction_time: dayjs(record.construction_time),
    };
    form.setFieldsValue(formData);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    setData(data.filter((item) => item.id !== id));
    message.success('删除成功！');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = {
        ...values,
        construction_time: values.construction_time.format('YYYY-MM-DD'),
      };

      if (editingItem) {
        // 编辑
        setData(
          data.map((item) =>
            item.id === editingItem.id ? { ...item, ...formattedValues } : item,
          ),
        );
        message.success('编辑成功！');
      } else {
        // 新增
        const newId = Math.max(...data.map((item) => item.id)) + 1;
        setData([...data, { id: newId, ...formattedValues }]);
        message.success('添加成功！');
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 获取历史要素类型选项
  const getHistoricalTypes = () => {
    return typeDict.filter((type) =>
      type.type_code.startsWith('HISTORICAL_TYPE_'),
    );
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '类型',
      dataIndex: 'type_dict_id',
      key: 'type_dict_id',
      render: (typeId: number) => {
        const type = typeDict.find((t) => t.id === typeId);
        return type?.type_name || '未知类型';
      },
    },
    {
      title: '建造时间',
      dataIndex: 'construction_time',
      key: 'construction_time',
      render: (time: string) => dayjs(time).format('YYYY年MM月DD日'),
    },
    {
      title: '所属区域',
      dataIndex: 'region_dict_id',
      key: 'region_dict_id',
      render: (regionId: number) => {
        const region = regionDict.find((r) => r.id === regionId);
        return region?.region_name || '未知区域';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <Title level={2}>历史要素管理</Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          添加历史要素
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />

      <Modal
        title={editingItem ? '编辑历史要素' : '添加历史要素'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={700}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入历史要素名称' }]}
          >
            <Input placeholder="请输入历史要素名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="编号"
            rules={[{ required: true, message: '请输入历史要素编号' }]}
          >
            <Input placeholder="请输入历史要素编号" />
          </Form.Item>

          <Form.Item
            name="type_dict_id"
            label="类型"
            rules={[{ required: true, message: '请选择历史要素类型' }]}
          >
            <Select placeholder="请选择历史要素类型">
              {getHistoricalTypes().map((type) => (
                <Select.Option key={type.id} value={type.id}>
                  {type.type_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="construction_longitude"
            label="经度"
            rules={[{ required: true, message: '请输入经度' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入经度"
              precision={6}
              min={-180}
              max={180}
            />
          </Form.Item>

          <Form.Item
            name="construction_latitude"
            label="纬度"
            rules={[{ required: true, message: '请输入纬度' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入纬度"
              precision={6}
              min={-90}
              max={90}
            />
          </Form.Item>

          <Form.Item
            name="location_description"
            label="位置描述"
            rules={[{ required: true, message: '请输入位置描述' }]}
          >
            <Input placeholder="请输入位置描述" />
          </Form.Item>

          <Form.Item
            name="construction_time"
            label="建造时间"
            rules={[{ required: true, message: '请选择建造时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="请选择建造时间"
            />
          </Form.Item>

          <Form.Item
            name="region_dict_id"
            label="所属区域"
            rules={[{ required: true, message: '请选择所属区域' }]}
          >
            <Select placeholder="请选择所属区域">
              {regionDict.map((region) => (
                <Select.Option key={region.id} value={region.id}>
                  {region.region_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="historical_records"
            label="历史记载"
            rules={[{ required: true, message: '请输入历史记载' }]}
          >
            <TextArea rows={4} placeholder="请输入历史记载" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminHistoricalElement;
