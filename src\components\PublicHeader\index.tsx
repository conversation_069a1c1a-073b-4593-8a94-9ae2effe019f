import { history, useLocation } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';

const PublicHeader: React.FC = () => {
  const location = useLocation();

  const navItems = [
    { path: '/', label: '首页' },
    { path: '/mountain', label: '山塬' },
    { path: '/water-system', label: '水系' },
    { path: '/historical-element', label: '历史要素' },
    { path: '/digital', label: '数字化' },
  ];

  const handleNavClick = (path: string) => {
    history.push(path);
  };

  const handleAdminClick = () => {
    history.push('/admin/login');
  };

  return (
    <div className="public-header">
      <div className="public-logo">
        <span>智慧营建系统</span>
      </div>

      <nav className="public-nav">
        {navItems.map((item) => (
          <a
            key={item.path}
            className={`public-nav-item ${
              location.pathname === item.path ? 'active' : ''
            }`}
            onClick={() => handleNavClick(item.path)}
          >
            {item.label}
          </a>
        ))}
      </nav>

      <Button className="admin-entry" onClick={handleAdminClick}>
        管理端
      </Button>
    </div>
  );
};

export default PublicHeader;
