# 地图地名重影问题修复

## 问题描述

在地图显示中出现地名重影现象，即同一个地名显示了两层，影响地图的可读性和用户体验。

## 问题原因分析

通过代码分析发现，地名重影问题主要由以下原因造成：

### 1. 重复的默认图层

在 `src/components/GaoDeMap/hooks.ts` 中：

```typescript
// 问题代码
const map = new AMap.Map(mapContainer.current, {
  // ... 其他配置
  defaultLayer: true, // 自动添加默认图层
});

// 然后又手动添加了默认图层
const defaultLayer = new AMap.TileLayer({
  zooms: [3, 20],
  visible: true,
});
map.add(defaultLayer);
```

这导致了两个相同的基础瓦片图层叠加显示。

### 2. 重复的建筑物图层

- 在 `hooks.ts` 中添加了一个建筑物图层
- 在 `index.tsx` 的 `onMapCreated` 回调中又添加了一个建筑物图层

这导致了建筑物和相关标签的重复显示。

## 修复方案

### 1. 修复重复的默认图层

**文件：** `src/components/GaoDeMap/hooks.ts`

```typescript
// 修复后的代码
const map = new AMap.Map(mapContainer.current, {
  // ... 其他配置
  defaultLayer: false, // 禁用自动默认图层
});

// 手动添加基础瓦片图层，设置合适的层级
const baseLayer = new AMap.TileLayer({
  zooms: [3, 20],
  visible: true,
  zIndex: 1, // 设置基础图层的层级
});
map.add(baseLayer);
```

### 2. 移除重复的建筑物图层

**文件：** `src/components/GaoDeMap/hooks.ts`

移除了在 hooks 中添加建筑物图层的代码，只在 `onMapCreated` 回调中添加一次。

### 3. 优化图层层级设置

**文件：** `src/components/GaoDeMap/index.tsx`

为各个图层设置了合适的 zIndex，避免图层重叠：

```typescript
const satelliteLayer = new AMap.TileLayer.Satellite({
  zIndex: 2, // 卫星图层层级
});
const roadNetLayer = new AMap.TileLayer.RoadNet({
  zIndex: 3, // 路网图层层级
});
const buildingLayer = new AMap.Buildings({
  zooms: [14, 20],
  zIndex: 10, // 建筑物图层层级最高
  heightFactor: 2,
});
```

## 修复效果

修复后的地图应该：

1. ✅ 地名标签不再重影
2. ✅ 地图显示清晰
3. ✅ 图层层级合理
4. ✅ 性能得到优化（减少了重复图层）

## 测试验证

1. 启动开发服务器：`npm run dev`
2. 访问 http://localhost:8000
3. 查看首页地图，确认地名不再重影
4. 测试地图的缩放、拖拽等功能正常

## 相关文件

- `src/components/GaoDeMap/hooks.ts` - 地图实例创建和管理
- `src/components/GaoDeMap/index.tsx` - 地图组件主文件
- `config/config.ts` - 地图配置

## 注意事项

1. 确保不要在多个地方重复添加相同的图层
2. 为不同图层设置合适的 zIndex 避免重叠
3. 使用 `defaultLayer: false` 时需要手动添加基础图层
4. 图层的添加顺序和层级设置很重要

## 后续优化建议

1. 可以考虑添加图层管理器，统一管理所有图层
2. 可以添加图层切换控件，让用户自由选择显示的图层
3. 可以根据缩放级别动态显示/隐藏某些图层以提升性能
