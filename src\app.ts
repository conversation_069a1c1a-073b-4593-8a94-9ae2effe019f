// 运行时配置

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{
  name: string;
  currentUser?: any;
  isAdmin?: boolean;
}> {
  return {
    name: '智慧营建系统',
    currentUser: null,
    isAdmin: false,
  };
}

// 管理端才使用layout布局
export const layout = () => {
  return {
    logo: '/logo.png',
    title: '智慧营建管理系统',
    menu: {
      locale: false,
    },
    // 只在管理端路由下显示layout
    disableContentMargin: false,
  };
};
