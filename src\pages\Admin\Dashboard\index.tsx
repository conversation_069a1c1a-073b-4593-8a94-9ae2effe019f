import {
  historicalElementData,
  mountainData,
  statisticData,
  waterSystemData,
} from '@/services/mockData';
import {
  BookOutlined,
  EnvironmentOutlined,
  FileImageOutlined,
  GlobalOutlined,
  HistoryOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Card, Col, List, Row, Statistic, Typography } from 'antd';
import ReactECharts from 'echarts-for-react';
import React from 'react';

const { Title } = Typography;

const AdminDashboard: React.FC = () => {
  // 最近添加的数据
  const recentData = [
    { title: '骊山', type: '山塬', time: '2024-08-15', avatar: '⛰️' },
    { title: '渭河', type: '水系', time: '2024-08-14', avatar: '🌊' },
    { title: '大雁塔', type: '历史要素', time: '2024-08-13', avatar: '🏛️' },
  ];

  // 饼图配置
  const pieOption = {
    title: {
      text: '要素类型分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12,
      },
    },
    series: [
      {
        name: '要素数量',
        type: 'pie',
        radius: ['40%', '70%'],
        data: statisticData.typeDistribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  // 柱状图配置
  const barOption = {
    title: {
      text: '区域分布统计',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: statisticData.regionDistribution.map((item) => item.name),
      axisLabel: {
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10,
      },
    },
    series: [
      {
        name: '要素数量',
        type: 'bar',
        data: statisticData.regionDistribution.map((item) => item.value),
        itemStyle: {
          color: '#1890ff',
        },
      },
    ],
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        仪表盘
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="山塬总数"
              value={mountainData.length}
              prefix={<EnvironmentOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="水系总数"
              value={waterSystemData.length}
              prefix={<GlobalOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="历史要素总数"
              value={historicalElementData.length}
              prefix={<HistoryOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={2}
              prefix={<UserOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表和列表 */}
      <Row gutter={16}>
        <Col span={8}>
          <Card title="要素类型分布" style={{ height: 400 }}>
            <ReactECharts option={pieOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="区域分布统计" style={{ height: 400 }}>
            <ReactECharts option={barOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="最近更新" style={{ height: 400 }}>
            <List
              itemLayout="horizontal"
              dataSource={recentData}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar>{item.avatar}</Avatar>}
                    title={item.title}
                    description={`${item.type} · ${item.time}`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => (window.location.href = '/admin/mountain')}
          >
            <EnvironmentOutlined
              style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }}
            />
            <div>管理山塬数据</div>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => (window.location.href = '/admin/upload')}
          >
            <FileImageOutlined
              style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }}
            />
            <div>上传资源文件</div>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => (window.location.href = '/admin/dictionary')}
          >
            <BookOutlined
              style={{ fontSize: 48, color: '#fa8c16', marginBottom: 16 }}
            />
            <div>管理字典数据</div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AdminDashboard;
