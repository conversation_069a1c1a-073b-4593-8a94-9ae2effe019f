import React, { useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { MapLoadingStatus } from './components';
import { useMapAPI, useMapInstance } from './hooks';
import styles from './index.less';

const Map: React.FC<MapProps> = (props) => {
  const {
    center,
    city = '西安',
    markers = [],
    zoom = 11,
    activedZoom = 15,
    enableCluster = false,
    enableInfoWindow = true,
    events = {},
    loadingText = '地图加载中...',
    onMapCreated,
    customControls = [],
    lazyLoading = false,
    timeoutMs = 5000,
    markerRender,
    style,
    className,
    wrapperStyle,
    wrapperClassName,
  } = props;

  // 创建DOM引用
  const mapContainer = useRef<HTMLDivElement>(null);

  // 使用自定义Hook加载高德地图API
  const {
    AMap,
    loading: apiLoading,
    error: apiError,
  } = useMapAPI(enableCluster);

  // 懒加载处理
  const [skipLoading, setSkipLoading] = useState(false);

  // 如果启用懒加载，检查容器是否在视图中
  React.useEffect(() => {
    if (lazyLoading && mapContainer.current) {
      if (!mapContainer.current.isConnected) {
        console.log('【懒加载】地图容器不在视图中，暂不加载地图');
        setSkipLoading(true);
      } else {
        setSkipLoading(false);
      }
    }
  }, [lazyLoading]);

  // 使用自定义Hook创建和管理地图实例
  const { mapInstance, mapReady } = useMapInstance({
    AMap,
    mapContainer,
    center,
    city,
    markers,
    zoom,
    activedZoom,
    mapOptions: {
      features: ['bg', 'road', 'building', 'point'], // 显示背景、道路、建筑物和兴趣点
      // viewMode: '3D', // 使用3D视图
      pitch: 45, // 俯仰角度（降低一点以便更好地看到建筑物）
      buildingAnimation: true, // 楼块出现是否带动画
      showBuildingBlock: true, // 是否显示楼块
      showLabel: true, // 显示文字标注
      skyColor: '#1f263a', // 天空颜色
    },
    enableCluster,
    enableInfoWindow,
    events,
    onMapCreated: (mapInst) => {
      // 创建图层（设置合适的层级避免重叠）
      const satelliteLayer = new AMap.TileLayer.Satellite({
        zIndex: 2, // 卫星图层层级
      });
      const roadNetLayer = new AMap.TileLayer.RoadNet({
        zIndex: 3, // 路网图层层级
      });
      const buildingLayer = new AMap.Buildings({
        zooms: [14, 20],
        zIndex: 10, // 建筑物图层层级最高
        heightFactor: 2,
      });

      // 默认添加建筑物图层
      mapInst.add(buildingLayer);

      // 将图层对象存储在地图实例上，以便在控件中访问
      mapInst.satelliteLayer = satelliteLayer;
      mapInst.roadNetLayer = roadNetLayer;
      mapInst.buildingLayer = buildingLayer;

      // 调用原始的onMapCreated回调
      if (onMapCreated) {
        onMapCreated(mapInst);
      }
    },
    customControls,
    timeoutMs,
    markerRender,
  });

  // 计算加载状态
  const isLoading = apiLoading && !skipLoading;
  const errorMessage = apiError;

  return (
    <div
      className={`${styles.mapWrapper} ${wrapperClassName || ''}`}
      style={wrapperStyle}
    >
      <div
        ref={mapContainer}
        className={`${styles.mapContainer} ${className || ''}`}
        style={style}
      />
      <MapLoadingStatus
        loading={isLoading}
        error={errorMessage}
        loadingText={loadingText}
      />
      {mapReady && mapInstance && customControls.length > 0 && (
        <>
          {customControls.map((control, index) => {
            // 创建控件容器元素
            const controlContainer = document.getElementById(
              `custom-control-${index}`,
            );
            if (controlContainer) {
              // 使用ReactDOM.createPortal将React组件渲染到DOM节点
              return ReactDOM.createPortal(
                <div className={styles.customControl}>{control.content}</div>,
                controlContainer,
              );
            }
            return null;
          })}
        </>
      )}
    </div>
  );
};

export default Map;
