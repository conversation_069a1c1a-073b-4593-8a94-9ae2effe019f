import { defineConfig } from '@umijs/max';
import routes from './routes';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '智慧营建管理系统',
  },
  define: {
    // 高德地图配置
    AMAP_CONFIG: {
      // 高德地图 API Key（需要在高德开放平台申请）
      key: process.env.AMAP_KEY || '60b9934af1d9787dfff1725c8494d55e',

      // 高德地图安全密钥（可选，用于更高的安全性）
      securityJsCode:
        process.env.AMAP_SECURITY_CODE || 'f40b7371e64d91c3c2fb78b9d03531db',

      // API 版本
      version: '2.0',

      // 默认插件
      plugins: [
        'AMap.Scale',
        'AMap.ToolBar',
        'AMap.InfoWindow',
        'AMap.Marker',
        'AMap.Geocoder',
      ],

      // 默认地图配置
      defaultMapOptions: {
        zoom: 11,
        center: [108.9398, 34.3412], // 关中地区中心坐标
        mapStyle: 'amap://styles/normal',
        features: ['bg', 'road', 'building', 'point'],
        showLabel: true,
      },

      // 标记点图标配置
      markerIcons: {
        mountain: '/images/markers/mountain.png',
        water: '/images/markers/water.png',
        historical: '/images/markers/historical.png',
        default: '/images/markers/default.png',
      },
    },
  },
  routes,
  npmClient: 'pnpm',
});
