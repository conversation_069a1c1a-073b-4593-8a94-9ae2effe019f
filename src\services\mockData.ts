// 模拟数据

// 区域字典数据
export const regionDict = [
  {
    id: 1,
    region_code: 'REGION_XIAN',
    region_name: '西安区域',
    parent_id: 0,
    status: 1,
    sort: 1,
    region_desc: '涵盖西安市及周边郊县',
  },
  {
    id: 2,
    region_code: 'REGION_XIANYANG',
    region_name: '咸阳区域',
    parent_id: 0,
    status: 1,
    sort: 2,
    region_desc: '涵盖咸阳市及周边区县',
  },
  {
    id: 3,
    region_code: 'REGION_BAOJI',
    region_name: '宝鸡区域',
    parent_id: 0,
    status: 1,
    sort: 3,
    region_desc: '涵盖宝鸡市及周边区县',
  },
  {
    id: 4,
    region_code: 'REGION_WEINAN',
    region_name: '渭南区域',
    parent_id: 0,
    status: 1,
    sort: 4,
    region_desc: '涵盖渭南市及周边区县',
  },
];

// 类型字典数据
export const typeDict = [
  {
    id: 1,
    type_code: 'MOUNTAIN_TYPE_HILL',
    type_name: '丘陵',
    parent_id: 0,
    status: 1,
    sort: 1,
    type_desc: '海拔500-1000米的山塬类型',
  },
  {
    id: 2,
    type_code: 'MOUNTAIN_TYPE_PLATEAU',
    type_name: '高原',
    parent_id: 0,
    status: 1,
    sort: 2,
    type_desc: '海拔1000米以上的山塬类型',
  },
  {
    id: 3,
    type_code: 'WATER_TYPE_RIVER',
    type_name: '河流',
    parent_id: 0,
    status: 1,
    sort: 1,
    type_desc: '天然或人工河道',
  },
  {
    id: 4,
    type_code: 'WATER_TYPE_LAKE',
    type_name: '湖泊',
    parent_id: 0,
    status: 1,
    sort: 2,
    type_desc: '天然或人工湖泊',
  },
  {
    id: 5,
    type_code: 'HISTORICAL_TYPE_TEMPLE',
    type_name: '寺庙',
    parent_id: 0,
    status: 1,
    sort: 1,
    type_desc: '宗教建筑',
  },
  {
    id: 6,
    type_code: 'HISTORICAL_TYPE_PALACE',
    type_name: '宫殿',
    parent_id: 0,
    status: 1,
    sort: 2,
    type_desc: '皇家建筑',
  },
  {
    id: 7,
    type_code: 'HISTORICAL_TYPE_TOMB',
    type_name: '陵墓',
    parent_id: 0,
    status: 1,
    sort: 3,
    type_desc: '古代墓葬',
  },
];

// 关系字典数据
export const relationshipDict = [
  {
    id: 1,
    relation_code: 'RELATION_LOCATION',
    relation_name: '选址关联',
    parent_id: 0,
    status: 1,
    sort: 1,
    relation_desc: '山塬与城镇的选址依赖关系',
  },
  {
    id: 2,
    relation_code: 'RELATION_WATER',
    relation_name: '水系滋养',
    parent_id: 0,
    status: 1,
    sort: 2,
    relation_desc: '水系对周边地区的滋养作用',
  },
  {
    id: 3,
    relation_code: 'RELATION_HISTORY',
    relation_name: '历史沿革',
    parent_id: 0,
    status: 1,
    sort: 3,
    relation_desc: '历史要素的传承发展关系',
  },
];

// 山塬数据
export const mountainData = [
  {
    id: 1,
    name: '骊山',
    code: 'MT001',
    longitude: 109.2144,
    latitude: 34.3668,
    height: 1302,
    historical_records:
      '骊山位于西安市临潼区，因其形似骊马而得名，是秦始皇陵的所在地。',
    region_dict_id: 1,
  },
  {
    id: 2,
    name: '华山',
    code: 'MT002',
    longitude: 110.091,
    latitude: 34.4883,
    height: 2154,
    historical_records: '华山为五岳之一，素有"奇险天下第一山"之称，道教圣地。',
    region_dict_id: 4,
  },
  {
    id: 3,
    name: '太白山',
    code: 'MT003',
    longitude: 107.7675,
    latitude: 34.0542,
    height: 3771,
    historical_records: '太白山是秦岭主峰，因山顶积雪终年不化而得名。',
    region_dict_id: 3,
  },
];

// 水系数据
export const waterSystemData = [
  {
    id: 1,
    name: '渭河',
    code: 'WS001',
    longitude: 108.9398,
    latitude: 34.3412,
    length_area: '818公里',
    historical_records:
      '渭河是黄河最大的支流，关中平原的母亲河，孕育了华夏文明。',
    region_dict_id: 1,
  },
  {
    id: 2,
    name: '泾河',
    code: 'WS002',
    longitude: 108.8062,
    latitude: 34.5292,
    length_area: '455公里',
    historical_records: '泾河是渭河的重要支流，"泾渭分明"典故的来源。',
    region_dict_id: 2,
  },
  {
    id: 3,
    name: '灞河',
    code: 'WS003',
    longitude: 109.1162,
    latitude: 34.2778,
    length_area: '104公里',
    historical_records:
      '灞河流经西安东部，古代送别之地，"灞桥折柳"的典故源于此。',
    region_dict_id: 1,
  },
];

// 历史要素数据
export const historicalElementData = [
  {
    id: 1,
    name: '大雁塔',
    code: 'HE001',
    type_dict_id: 5,
    construction_longitude: 108.9642,
    construction_latitude: 34.2186,
    location_description: '位于西安市雁塔区大慈恩寺内',
    construction_time: '652-01-01',
    historical_records:
      '大雁塔建于唐代，是玄奘法师为保存从印度带回的佛经而建造的佛塔。',
    region_dict_id: 1,
  },
  {
    id: 2,
    name: '秦始皇陵',
    code: 'HE002',
    type_dict_id: 7,
    construction_longitude: 109.2731,
    construction_latitude: 34.3848,
    location_description: '位于西安市临潼区骊山北麓',
    construction_time: '246-01-01',
    historical_records:
      '秦始皇陵是中国历史上第一位皇帝的陵寝，兵马俑的发现震惊世界。',
    region_dict_id: 1,
  },
  {
    id: 3,
    name: '大明宫',
    code: 'HE003',
    type_dict_id: 6,
    construction_longitude: 109.0298,
    construction_latitude: 34.2992,
    location_description: '位于西安市未央区',
    construction_time: '634-01-01',
    historical_records: '大明宫是唐朝的皇宫，是当时世界上最辉煌壮丽的宫殿群。',
    region_dict_id: 1,
  },
];

// 照片数据 - 使用本地图片
export const photoData = [
  {
    id: 1,
    name: '骊山全景',
    url: '/images/photos/photo1.jpg',
    mountain_id: 1,
    water_system_id: null,
    historical_element_id: null,
  },
  {
    id: 2,
    name: '骊山秋色',
    url: '/images/photos/photo2.jpg',
    mountain_id: 1,
    water_system_id: null,
    historical_element_id: null,
  },
  {
    id: 3,
    name: '华山北峰',
    url: '/images/photos/photo3.jpg',
    mountain_id: 2,
    water_system_id: null,
    historical_element_id: null,
  },
  {
    id: 4,
    name: '华山日出',
    url: '/images/photos/photo4.jpg',
    mountain_id: 2,
    water_system_id: null,
    historical_element_id: null,
  },
  {
    id: 5,
    name: '渭河风光',
    url: '/images/photos/photo5.jpg',
    mountain_id: null,
    water_system_id: 1,
    historical_element_id: null,
  },
  {
    id: 6,
    name: '大雁塔夜景',
    url: '/images/photos/photo6.jpg',
    mountain_id: null,
    water_system_id: null,
    historical_element_id: 1,
  },
  {
    id: 7,
    name: '兵马俑',
    url: '/images/photos/photo7.jpg',
    mountain_id: null,
    water_system_id: null,
    historical_element_id: 2,
  },
];

// 用户数据
export const userData = [
  { id: 1, username: 'admin', password: '123456', role: 'admin' },
  { id: 2, username: 'editor', password: '123456', role: 'editor' },
];

// 统计数据
export const statisticData = {
  summary: {
    mountainCount: mountainData.length,
    waterSystemCount: waterSystemData.length,
    historicalElementCount: historicalElementData.length,
    totalCount:
      mountainData.length +
      waterSystemData.length +
      historicalElementData.length,
  },
  regionDistribution: [
    { name: '西安区域', value: 5 },
    { name: '咸阳区域', value: 1 },
    { name: '宝鸡区域', value: 1 },
    { name: '渭南区域', value: 1 },
  ],
  typeDistribution: [
    { name: '山塬', value: mountainData.length },
    { name: '水系', value: waterSystemData.length },
    { name: '历史要素', value: historicalElementData.length },
  ],
  timelineData: [
    { year: '246', count: 1, name: '秦始皇陵' },
    { year: '634', count: 1, name: '大明宫' },
    { year: '652', count: 1, name: '大雁塔' },
  ],
};

// 地图标记点数据
export const mapData = {
  mountains: mountainData.map((item) => ({
    ...item,
    type: 'mountain',
    photos: photoData.filter((photo) => photo.mountain_id === item.id),
  })),
  waterSystems: waterSystemData.map((item) => ({
    ...item,
    type: 'waterSystem',
    photos: photoData.filter((photo) => photo.water_system_id === item.id),
  })),
  historicalElements: historicalElementData.map((item) => ({
    ...item,
    type: 'historicalElement',
    photos: photoData.filter(
      (photo) => photo.historical_element_id === item.id,
    ),
  })),
};
