import PublicLayout from '@/components/PublicLayout';
import { mountainData, regionDict } from '@/services/mockData';
import { ArrowUpOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button, Card, Tag, Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;

const MountainPage: React.FC = () => {
  const handleDetailClick = (id: number) => {
    history.push(`/detail/mountain/${id}`);
  };

  const getRegionName = (regionId: number) => {
    const region = regionDict.find((r) => r.id === regionId);
    return region?.region_name || '未知区域';
  };

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          关中地区山塬
        </Title>

        <Paragraph
          style={{ textAlign: 'center', fontSize: 16, marginBottom: 32 }}
        >
          山塬是关中地区重要的地理要素，承载着深厚的历史文化内涵
        </Paragraph>

        <div className="list-container">
          {mountainData.map((mountain) => (
            <Card
              key={mountain.id}
              className="list-item"
              hoverable
              onClick={() => handleDetailClick(mountain.id)}
              cover={
                <div
                  style={{
                    height: 200,
                    background: 'linear-gradient(45deg, #87CEEB, #98FB98)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: 48,
                    color: 'white',
                  }}
                >
                  ⛰️
                </div>
              }
            >
              <Card.Meta
                title={
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span>{mountain.name}</span>
                    <Tag color="blue">{mountain.code}</Tag>
                  </div>
                }
                description={
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <EnvironmentOutlined style={{ marginRight: 4 }} />
                      {getRegionName(mountain.region_dict_id)}
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <ArrowUpOutlined style={{ marginRight: 4 }} />
                      海拔：{mountain.height}米
                    </div>
                    <Paragraph
                      ellipsis={{ rows: 2 }}
                      style={{ marginBottom: 16, color: '#666' }}
                    >
                      {mountain.historical_records}
                    </Paragraph>
                    <Button type="primary" size="small">
                      查看详情
                    </Button>
                  </div>
                }
              />
            </Card>
          ))}
        </div>
      </div>
    </PublicLayout>
  );
};

export default MountainPage;
