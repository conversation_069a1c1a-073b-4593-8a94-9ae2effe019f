import PublicLayout from '@/components/PublicLayout';
import { regionDict, waterSystemData } from '@/services/mockData';
import { EnvironmentOutlined, GlobalOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button, Card, Tag, Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;

const WaterSystemPage: React.FC = () => {
  const handleDetailClick = (id: number) => {
    history.push(`/detail/waterSystem/${id}`);
  };

  const getRegionName = (regionId: number) => {
    const region = regionDict.find((r) => r.id === regionId);
    return region?.region_name || '未知区域';
  };

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          关中地区水系
        </Title>

        <Paragraph
          style={{ textAlign: 'center', fontSize: 16, marginBottom: 32 }}
        >
          水系是关中地区的生命之源，滋养着这片古老的土地
        </Paragraph>

        <div className="list-container">
          {waterSystemData.map((waterSystem) => (
            <Card
              key={waterSystem.id}
              className="list-item"
              hoverable
              onClick={() => handleDetailClick(waterSystem.id)}
              cover={
                <div
                  style={{
                    height: 200,
                    background: 'linear-gradient(45deg, #4FC3F7, #29B6F6)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: 48,
                    color: 'white',
                  }}
                >
                  🌊
                </div>
              }
            >
              <Card.Meta
                title={
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span>{waterSystem.name}</span>
                    <Tag color="cyan">{waterSystem.code}</Tag>
                  </div>
                }
                description={
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <EnvironmentOutlined style={{ marginRight: 4 }} />
                      {getRegionName(waterSystem.region_dict_id)}
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <GlobalOutlined style={{ marginRight: 4 }} />
                      长度：{waterSystem.length_area}
                    </div>
                    <Paragraph
                      ellipsis={{ rows: 2 }}
                      style={{ marginBottom: 16, color: '#666' }}
                    >
                      {waterSystem.historical_records}
                    </Paragraph>
                    <Button type="primary" size="small">
                      查看详情
                    </Button>
                  </div>
                }
              />
            </Card>
          ))}
        </div>
      </div>
    </PublicLayout>
  );
};

export default WaterSystemPage;
