import GaoDeMap from '@/components/GaoDeMap';
import PublicLayout from '@/components/PublicLayout';
import { mapData } from '@/services/mockData';
import { Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  // 生成信息窗口内容，包含图片和查看详情按钮
  const generateInfoWindowContent = (item: any, type: string) => {
    const detailUrl = `/detail/${type}/${item.id}`;
    let basicInfo = '';

    if (type === 'mountain') {
      basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">海拔：${item.height}米</p>`;
    } else if (type === 'waterSystem') {
      basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">长度：${item.length_area}</p>`;
    } else if (type === 'historicalElement') {
      basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">建造时间：${new Date(item.construction_time).getFullYear()}年</p>`;
    }

    // 获取第一张图片作为展示图片
    const firstPhoto = item.photos && item.photos.length > 0 ? item.photos[0] : null;
    const imageHtml = firstPhoto
      ? `<div style="position: relative; width: 100%; height: 140px; overflow: hidden;">
          <img src="${firstPhoto.url}" alt="${firstPhoto.name || item.name}"
               style="width: 100%; height: 100%; object-fit: cover; display: block;"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
          <div style="display: none; width: 100%; height: 100%; background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); align-items: center; justify-content: center; color: #999; font-size: 14px;">暂无图片</div>
         </div>`
      : ''; // `<div style="width: 100%; height: 140px; background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px;">暂无图片</div>`;

    return `<div style="padding: 0; min-width: 240px; max-width: 300px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
      ${imageHtml}
      <div style="padding: 12px;">
        <h4 style="margin: 0 0 6px 0; color: #333; font-size: 16px; font-weight: bold; line-height: 1.3;">${item.name}</h4>
        ${basicInfo}
        <p style="margin: 6px 0 12px 0; color: #666; font-size: 13px; line-height: 1.4; max-height: 54px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;">${item.historical_records}</p>
        <div style="text-align: center;">
          <a href="${detailUrl}" style="display: inline-block; padding: 8px 20px; background: #1890ff; color: white; text-decoration: none; border-radius: 4px; font-size: 13px; font-weight: 500; transition: all 0.3s ease;"
             onmouseover="this.style.backgroundColor='#40a9ff'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(24,144,255,0.3)';"
             onmouseout="this.style.backgroundColor='#1890ff'; this.style.transform='translateY(0)'; this.style.boxShadow='none';">查看详情</a>
        </div>
      </div>
    </div>`;
  };

  // 准备地图标记点数据
  const prepareMapMarkers = () => {
    const markers: any[] = [];

    // 添加山塬标记点
    mapData.mountains.forEach((mountain) => {
      markers.push({
        position: [mountain.longitude, mountain.latitude],
        title: mountain.name,
        content: generateInfoWindowContent(mountain, 'mountain'),
        id: `mountain_${mountain.id}`,
        status: 1, // 山塬状态
        icon: {
          image: AMAP_CONFIG.markerIcons.mountain,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...mountain, type: 'mountain' },
      });
    });

    // 添加水系标记点
    mapData.waterSystems.forEach((waterSystem) => {
      markers.push({
        position: [waterSystem.longitude, waterSystem.latitude],
        title: waterSystem.name,
        content: generateInfoWindowContent(waterSystem, 'waterSystem'),
        id: `water_${waterSystem.id}`,
        status: 2, // 水系状态
        icon: {
          image: AMAP_CONFIG.markerIcons.water,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...waterSystem, type: 'waterSystem' },
      });
    });

    // 添加历史要素标记点
    mapData.historicalElements.forEach((element) => {
      markers.push({
        position: [
          element.construction_longitude,
          element.construction_latitude,
        ],
        title: element.name,
        content: generateInfoWindowContent(element, 'historicalElement'),
        id: `historical_${element.id}`,
        status: 3, // 历史要素状态
        icon: {
          image: AMAP_CONFIG.markerIcons.historical,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...element, type: 'historicalElement' },
      });
    });

    return markers;
  };

  // 地图标记点点击事件处理
  const handleMarkerClick = (marker: any, e: any) => {
    console.log('标记点点击:', marker, e);
    // 不再显示自定义Modal，统一使用地图信息窗口
  };

  // 地图点击事件处理
  const handleMapClick = (e: any) => {
    console.log('地图点击:', e);
  };

  // 地图创建完成回调
  const handleMapCreated = (mapInstance: any) => {
    console.log('地图创建完成:', mapInstance);
  };



  return (
    <PublicLayout>
      <div className="content-card">
        <div style={{ padding: '24px 24px 0' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>
            关中地区智慧营建系统
          </Title>
          <Paragraph
            style={{ textAlign: 'center', fontSize: 16, marginBottom: 24 }}
          >
            探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
          </Paragraph>
        </div>

        <div className="map-container">
          <GaoDeMap
            city="西安"
            center={[108.9398, 34.3412]} // 关中地区中心坐标
            zoom={8}
            activedZoom={12}
            markers={prepareMapMarkers()}
            enableInfoWindow={true}
            enableCluster={false}
            events={{
              onClick: handleMapClick,
              onMarkerClick: handleMarkerClick,
            }}
            onMapCreated={handleMapCreated}
            style={{ width: '100%', height: '100%' }}
          />
        </div>

        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Paragraph>
            点击地图上的标记点查看详细信息，或通过导航栏浏览不同类型的要素
          </Paragraph>
        </div>
      </div>


    </PublicLayout>
  );
};

export default HomePage;
