import '@umijs/max/typings';

declare global {
  interface Window {
    AMap: any;
  }

  // 高德地图配置类型声明
  declare const AMAP_CONFIG: {
    /** 高德地图 API Key */
    key: string;
    /** 高德地图安全密钥 */
    securityJsCode: string;
    /** API 版本 */
    version: string;
    /** 默认插件列表 */
    plugins: string[];
    /** 默认地图配置 */
    defaultMapOptions: {
      zoom: number;
      center: [number, number];
      mapStyle: string;
      features: string[];
      showLabel: boolean;
    };
    /** 标记点图标配置 */
    markerIcons: {
      mountain: string;
      water: string;
      historical: string;
      default: string;
    };
  };
}
