import { userData } from '@/services/mockData';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import React, { useState } from 'react';

const { Title } = Typography;

const AdminUser: React.FC = () => {
  const [data, setData] = useState(userData);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [form] = Form.useForm();

  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingItem(record);
    // 不显示密码
    const { password, ...formData } = record;
    void password;
    form.setFieldsValue(formData);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    if (data.length <= 1) {
      message.error('至少需要保留一个用户！');
      return;
    }
    setData(data.filter((item) => item.id !== id));
    message.success('删除成功！');
  };

  const handleResetPassword = (record: any) => {
    Modal.confirm({
      title: '重置密码',
      content: `确定要将用户 ${record.username} 的密码重置为 "123456" 吗？`,
      onOk() {
        setData(
          data.map((item) =>
            item.id === record.id ? { ...item, password: '123456' } : item,
          ),
        );
        message.success('密码重置成功！新密码为：123456');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 检查用户名是否重复
      const existingUser = data.find(
        (user) =>
          user.username === values.username && user.id !== editingItem?.id,
      );
      if (existingUser) {
        message.error('用户名已存在！');
        return;
      }

      if (editingItem) {
        // 编辑（不修改密码）
        setData(
          data.map((item) =>
            item.id === editingItem.id ? { ...item, ...values } : item,
          ),
        );
        message.success('编辑成功！');
      } else {
        // 新增
        const newId = Math.max(...data.map((item) => item.id)) + 1;
        const newUser = {
          id: newId,
          ...values,
          password: values.password || '123456', // 默认密码
        };
        setData([...data, newUser]);
        message.success('添加成功！');
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (username: string) => (
        <Space>
          <UserOutlined />
          {username}
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => {
        const color = role === 'admin' ? 'red' : 'blue';
        const text = role === 'admin' ? '管理员' : '编辑员';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      render: () => '2024-08-15 10:00:00', // 模拟数据
    },
    {
      title: '最后登录',
      key: 'lastLogin',
      render: () => '2024-08-15 14:30:00', // 模拟数据
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button type="link" onClick={() => handleResetPassword(record)}>
            重置密码
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <Title level={2}>用户管理</Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          添加用户
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个用户`,
        }}
      />

      <Modal
        title={editingItem ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={500}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            role: 'editor',
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          {!editingItem && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Select.Option value="admin">管理员</Select.Option>
              <Select.Option value="editor">编辑员</Select.Option>
            </Select>
          </Form.Item>

          {editingItem && (
            <div
              style={{
                background: '#f6f6f6',
                padding: '12px',
                borderRadius: '6px',
                marginBottom: '16px',
              }}
            >
              <p style={{ margin: 0, color: '#666' }}>
                注意：编辑用户不会修改密码，如需重置密码请使用&quot;重置密码&quot;功能
              </p>
            </div>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUser;
