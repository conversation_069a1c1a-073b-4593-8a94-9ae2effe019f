# 图片资源目录

这个目录用于存放项目中使用的图片资源。

## 目录结构

### markers/ - 地图标记点图标（统一Google Maps风格）
- `mountain.png` - 山塬标记图标（蓝色圆点）
- `water.png` - 水系标记图标（红色圆点）
- `historical.png` - 历史要素标记图标（绿色圆点）
- `default.png` - 默认标记图标（紫色圆点）

### photos/ - 项目展示图片
- `photo1.jpg` - 骊山全景
- `photo2.jpg` - 骊山秋色
- `photo3.jpg` - 华山北峰
- `photo4.jpg` - 华山日出
- `photo5.jpg` - 渭河风光
- `photo6.jpg` - 大雁塔夜景
- `photo7.jpg` - 兵马俑

## 资源来源

- **地图标记图标**：原来自高德地图API，现已下载到本地
- **展示图片**：原来自picsum.photos占位图片服务，现已下载到本地

## 使用说明

所有图片资源都已本地化，不再依赖外部服务，提高了项目的稳定性和加载速度。

## 配置更新

相关配置文件已更新：
- `config/config.ts` - 更新了地图标记图标路径
- `src/services/mockData.ts` - 更新了展示图片路径
