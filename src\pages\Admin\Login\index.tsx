import { userData } from '@/services/mockData';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Button, Card, Form, Input, Typography, message } from 'antd';
import React, { useState } from 'react';

const { Title } = Typography;

const AdminLogin: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { setInitialState } = useModel('@@initialState');

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);

    try {
      // 模拟登录验证
      const user = userData.find(
        (u) => u.username === values.username && u.password === values.password,
      );

      if (user) {
        // 登录成功，更新全局状态
        await setInitialState({
          name: '智慧营建系统',
          currentUser: user,
          isAdmin: user.role === 'admin',
        });

        message.success('登录成功！');
        history.push('/admin/dashboard');
      } else {
        message.error('用户名或密码错误！');
      }
    } catch (error) {
      message.error('登录失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Card className="login-form">
        <Title level={2} className="login-title">
          智慧营建管理系统
        </Title>

        <Form name="login" onFinish={onFinish} autoComplete="off" size="large">
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%' }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 16, color: '#666' }}>
          <div>测试账号：</div>
          <div>管理员 - 用户名: admin, 密码: 123456</div>
          <div>编辑员 - 用户名: editor, 密码: 123456</div>
        </div>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button type="link" onClick={() => history.push('/')}>
            返回首页
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default AdminLogin;
